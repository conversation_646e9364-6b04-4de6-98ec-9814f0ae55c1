{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "pipeline": {"build": {"dependsOn": ["^build"], "outputs": [".next/**", "!.next/cache/**", "dist/**"]}, "dev": {"cache": false, "persistent": true}, "lint": {"dependsOn": ["^lint"]}, "type-check": {"dependsOn": ["^type-check"]}, "test": {"dependsOn": ["^build"]}, "clean": {"cache": false}, "db:push": {"cache": false}, "db:migrate": {"cache": false}, "db:studio": {"cache": false, "persistent": true}, "postinstall": {"cache": false}}}