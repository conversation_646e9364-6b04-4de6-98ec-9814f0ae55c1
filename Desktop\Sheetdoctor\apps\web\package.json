{"name": "@sheetdoctor/web", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "clean": "rm -rf .next"}, "dependencies": {"next": "^15.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@trpc/client": "^10.45.0", "@trpc/server": "^10.45.0", "@trpc/react-query": "^10.45.0", "@tanstack/react-query": "^5.0.0", "zod": "^3.22.0", "zustand": "^4.4.0", "framer-motion": "^10.16.0", "date-fns": "^2.30.0", "lodash": "^4.17.21", "lucide-react": "^0.292.0", "@sheetdoctor/ui": "workspace:*", "@sheetdoctor/database": "workspace:*", "@sheetdoctor/auth": "workspace:*", "@sheetdoctor/ai": "workspace:*", "@sheetdoctor/types": "workspace:*"}, "devDependencies": {"@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/lodash": "^4.14.0", "typescript": "^5.2.0", "eslint": "^8.0.0", "eslint-config-next": "^15.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0"}}