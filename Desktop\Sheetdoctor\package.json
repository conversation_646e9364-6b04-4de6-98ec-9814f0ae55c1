{"name": "sheetdoctor", "version": "0.1.0", "private": true, "description": "SaaS Excel formula fixer with AI-powered suggestions", "workspaces": ["apps/*", "packages/*"], "scripts": {"dev": "turbo run dev", "build": "turbo run build", "test": "turbo run test", "lint": "turbo run lint", "type-check": "turbo run type-check", "clean": "turbo run clean", "db:push": "turbo run db:push", "db:migrate": "turbo run db:migrate", "db:studio": "turbo run db:studio", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "postinstall": "turbo run postinstall"}, "devDependencies": {"@turbo/gen": "^1.10.12", "turbo": "^1.10.12", "prettier": "^3.0.0", "typescript": "^5.2.0"}, "packageManager": "bun@1.0.0", "engines": {"node": ">=18.0.0", "bun": ">=1.0.0"}}