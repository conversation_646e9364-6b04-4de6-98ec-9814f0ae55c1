# SheetDoctor 🩺📊

AI-powered Excel formula fixer that helps users identify and resolve formula errors with intelligent suggestions.

## 🚀 Quick Start

```bash
# Install Bun (if not already installed)
curl -fsSL https://bun.sh/install | bash

# Clone and setup
git clone <repo-url>
cd sheetdoctor
bun install

# Setup database
bun db:push

# Start development
bun dev
```

## 🏗️ Architecture

This is a Turborepo monorepo with the following structure:

```
sheetdoctor/
├── apps/
│   ├── web/                    # Next.js 15 web app
│   └── excel-addin/           # Office.js Excel Add-in
├── packages/
│   ├── ui/                    # Shared UI components (Shadcn/ui)
│   ├── database/              # Prisma schema & migrations
│   ├── auth/                  # BetterAuth configuration
│   ├── ai/                    # AI prompt engineering & utilities
│   └── types/                 # Shared TypeScript types
├── docs/                      # Documentation
└── scripts/                   # Build & deployment scripts
```

## 🛠️ Technology Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript (strict mode)
- **Package Manager**: Bun
- **Monorepo**: Turborepo
- **Database**: PostgreSQL with Prisma
- **Authentication**: BetterAuth
- **Payments**: Polar.sh
- **AI**: OpenAI GPT-4o / Claude 3.5 Sonnet
- **UI**: Shadcn/ui + Tailwind CSS
- **Excel Integration**: Office.js

## 📝 Development

### Available Scripts

- `bun dev` - Start development servers
- `bun build` - Build all apps and packages
- `bun test` - Run tests
- `bun lint` - Lint code
- `bun type-check` - Type check
- `bun db:push` - Push database schema
- `bun db:migrate` - Run database migrations
- `bun db:studio` - Open Prisma Studio

### Code Standards

- TypeScript strict mode
- Functional components with hooks
- Zod for validation
- Feature-based folder structure
- Barrel exports for clean imports

## 🔧 Environment Setup

Copy `.env.example` to `.env.local` and fill in the required values:

```bash
cp .env.example .env.local
```

## 📚 Documentation

- [Architecture Guide](./docs/architecture.md)
- [API Documentation](./docs/api.md)
- [Excel Add-in Guide](./docs/excel-addin.md)
- [Deployment Guide](./docs/deployment.md)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

MIT License - see [LICENSE](./LICENSE) for details.
