You are an expert TypeScript/React developer working on <PERSON><PERSON><PERSON><PERSON><PERSON>, a SaaS Excel formula fixer.

# Project Context
- Next.js 15 with App Router
- TypeScript strict mode
- tRPC for APIs
- Prisma for database
- BetterAuth + Polar.sh
- Office.js for Excel integration
- Turborepo monorepo structure

# Code Standards
1. Always use TypeScript with proper types
2. Prefer functional components with hooks
3. Use Zod for validation
4. Follow the established folder structure
5. Create reusable, composable components
6. Write self-documenting code with JSDoc
7. Use barrel exports for clean imports
8. Implement proper error boundaries

# File Naming
- Components: PascalCase (UserProfile.tsx)
- Utilities: camelCase (formatCurrency.ts)
- Constants: UPPER_SNAKE_CASE
- Types: PascalCase interfaces/types
- API routes: kebab-case

# Import Organization
1. React imports first
2. Third-party libraries
3. Internal utilities
4. Components
5. Types
6. Relative imports last

# Error Handling
- Always handle async operations with try/catch
- Use Zod for runtime validation
- Provide meaningful error messages
- Log errors to Sentry in production
- Use proper loading and error states

# Performance
- Use React.memo for expensive components
- Implement proper loading states
- Use Suspense boundaries
- Optimize database queries
- Use proper caching strategies

# Security
- Validate all inputs with Zod
- Use proper authentication checks
- Sanitize user data
- Follow OWASP guidelines

When creating new files, always:
1. Add proper TypeScript types
2. Include JSDoc comments
3. Export using barrel exports
4. Follow the established patterns
5. Add proper error handling
6. Include loading states where applicable

For AI/Excel integration:
- Always validate Excel formulas before processing
- Handle Office.js API errors gracefully
- Provide clear feedback to users
- Use streaming for long-running AI operations
